.meteorologicalAlarmDialog,
.soilOfHistoryDialog,
.soilAlarmDialog,
.InsectSituationAlarmDialog,
.InsectSituationOfHistoryDialog{
  .el-dialog{
    width: 1320px;
    height: 592px;
    margin-top: 25vh !important;
    .el-dialog__body{
      height: 526px;
      .line{
        width: 1250px;
        margin: auto;
      }
      .handleBox{
        display: flex;
        margin-top: 28px;
        &_item{
          margin-right: 30px;
          .el-select{
            width: 180px;
            height: 32px;
            .el-input__icon {
              line-height: 32px;
            }
          }
          .el-date-editor,
          .el-range-separator{
            height: 32px !important;
            .el-input__icon {
              line-height: 27px;
            }
          }
          .el-button{
            width: 110px;
            height: 32px;
            line-height: 10px;
          }
        }
      }
      .tableBox{
        height: 405px;
        margin-top: 31px;
        .cell{
          .viewData{
            font-size: 15px;
            font-weight: 400;
            color: #007EFF;
            cursor: pointer;
            margin: 0 8px;
          }
          .img{
            width: 200px;
            height: 118px;
          }
          img{
            vertical-align: middle;
            cursor: pointer;
            margin: 0 5px;
          }
        }
      }
    }
  }
}
.meteorologyOfHistoryDialog{
.el-dialog{
    width: 1320px;
    height: 780px;
    margin-top: 10vh !important;
    .el-dialog__body{
      height: 780px;
      .line{
        width: 1250px;
        margin: auto;
      }
      .handleBox{
        display: flex;
        margin-top: 28px;
        &_item{
          margin-right: 30px;
          .el-select{
            width: 180px;
            height: 32px;
            .el-input__icon {
              line-height: 32px;
            }
          }
          .el-date-editor,
          .el-range-separator{
            height: 32px !important;
            .el-input__icon {
              line-height: 27px;
            }
          }
          .el-button{
            width: 110px;
            height: 32px;
            line-height: 10px;
          }
        }
      }
      .tableBox{
        height: 405px;
        margin-top: 31px;
        .cell{
          .viewData{
            font-size: 15px;
            font-weight: 400;
            color: #007EFF;
            cursor: pointer;
            margin: 0 8px;
          }
          .img{
            width: 200px;
            height: 118px;
          }
          img{
            vertical-align: middle;
            cursor: pointer;
            margin: 0 5px;
          }
        }
      }
    }
  }
  }